package vn.lukepham.statemachine;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for sending notifications to healthcare providers.
 * In a production system, this would integrate with email, SMS, or push notification services.
 */
@Slf4j
@Service
public class NotificationService {

    public void sendInfoRequest(String treatmentId, String providerId, String infoRequired) {
        log.info("Sending info request notification - Treatment: {}, Provider: {}, Info needed: {}", 
            treatmentId, providerId, infoRequired);
        // Implementation would send actual notification (email, SMS, etc.)
    }

    public void sendApprovalNotification(String treatmentId, String providerId) {
        log.info("Sending approval notification - Treatment: {}, Provider: {}", 
            treatmentId, providerId);
        // Implementation would send actual notification
    }

    public void sendDenialNotification(String treatmentId, String providerId, String denialReason) {
        log.info("Sending denial notification - Treatment: {}, Provider: {}, Reason: {}", 
            treatmentId, providerId, denialReason);
        // Implementation would send actual notification
    }
}