package vn.lukepham.statemachine;

import com.insurance.statemachine.dto.EventRequest;
import com.insurance.statemachine.dto.TreatmentRequest;
import com.insurance.statemachine.dto.TreatmentResponse;
import com.insurance.statemachine.exception.InvalidStateTransitionException;
import com.insurance.statemachine.exception.TreatmentNotFoundException;
import com.insurance.statemachine.service.TreatmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vn.lukepham.statemachine.Events;
import vn.lukepham.statemachine.States;

import javax.validation.Valid;

/**
 * REST Controller for managing treatment authorization requests.
 * Provides endpoints for creating treatments, checking status, and processing state transitions.
 */
@Slf4j
@RestController
@RequestMapping("/api/treatments")
@RequiredArgsConstructor
@Validated
@Tag(name = "Treatment Authorization", description = "Endpoints for managing treatment authorization requests")
public class TreatmentController {

    private final TreatmentService treatmentService;

    /**
     * Creates a new treatment authorization request.
     * Initializes the state machine in TREATMENT_SUBMITTED state.
     */
    @PostMapping
    @PreAuthorize("hasRole('PROVIDER')")
    @Operation(summary = "Create a new treatment authorization request")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Treatment created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<TreatmentResponse> createTreatment(
            @Valid @RequestBody TreatmentRequest request) {
        
        log.info("Creating new treatment authorization for patient: {}", request.getPatientName());
        
        TreatmentResponse response = treatmentService.createTreatment(request);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Retrieves the current status and details of a treatment request.
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('PROVIDER', 'ADJUSTER')")
    @Operation(summary = "Get treatment authorization details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Treatment found"),
        @ApiResponse(responseCode = "404", description = "Treatment not found"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<TreatmentResponse> getTreatment(
            @Parameter(description = "Treatment ID") @PathVariable String id) {
        
        log.info("Retrieving treatment authorization: {}", id);
        
        TreatmentResponse response = treatmentService.getTreatment(id);
        
        return ResponseEntity.ok(response);
    }

    /**
     * Processes an event for a treatment authorization.
     * Triggers state transition based on the provided event.
     */
    @PostMapping("/{id}/events")
    @PreAuthorize("hasAnyRole('PROVIDER', 'ADJUSTER')")
    @Operation(summary = "Process an event for a treatment authorization")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Event processed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Treatment not found"),
        @ApiResponse(responseCode = "409", description = "Invalid state transition"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions for this event")
    })
    public ResponseEntity<TreatmentResponse> processEvent(
            @Parameter(description = "Treatment ID") @PathVariable String id,
            @Valid @RequestBody EventRequest eventRequest) {
        
        log.info("Processing event {} for treatment: {}", eventRequest.getEvent(), id);
        
        TreatmentResponse response = treatmentService.processEvent(id, eventRequest);
        
        return ResponseEntity.ok(response);
    }

    /**
     * Exception handler for treatment not found.
     */
    @ExceptionHandler(TreatmentNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleTreatmentNotFound(TreatmentNotFoundException ex) {
        ErrorResponse error = new ErrorResponse(
            HttpStatus.NOT_FOUND.value(),
            "Treatment Not Found",
            ex.getMessage()
        );
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    /**
     * Exception handler for invalid state transitions.
     */
    @ExceptionHandler(InvalidStateTransitionException.class)
    public ResponseEntity<ErrorResponse> handleInvalidStateTransition(InvalidStateTransitionException ex) {
        ErrorResponse error = new ErrorResponse(
            HttpStatus.CONFLICT.value(),
            "Invalid State Transition",
            ex.getMessage()
        );
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    /**
     * Exception handler for security exceptions.
     */
    @ExceptionHandler(SecurityException.class)
    public ResponseEntity<ErrorResponse> handleSecurityException(SecurityException ex) {
        ErrorResponse error = new ErrorResponse(
            HttpStatus.FORBIDDEN.value(),
            "Access Denied",
            ex.getMessage()
        );
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(error);
    }
}