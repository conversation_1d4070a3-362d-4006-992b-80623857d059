package vn.lukepham.statemachine;

/**
 * Enum representing all possible events that trigger state transitions.
 */
public enum Events {
    /**
     * Event to submit a new treatment request.
     * Transitions from nothing to TREATMENT_SUBMITTED.
     */
    SUBMIT("Submit Treatment", "Submit a new treatment authorization request"),
    
    /**
     * Event to assign a treatment for review.
     * Transitions from TREATMENT_SUBMITTED to UNDER_REVIEW.
     */
    ASSIGN_FOR_REVIEW("Assign for Review", "Assign the treatment request to a reviewer"),
    
    /**
     * Event to request additional information.
     * Transitions from UNDER_REVIEW to ADDITIONAL_INFO_REQUIRED.
     */
    REQUEST_INFO("Request Information", "Request additional information from the provider"),
    
    /**
     * Event when provider submits additional information.
     * Transitions from ADDITIONAL_INFO_REQUIRED back to UNDER_REVIEW.
     */
    PROVIDE_INFO("Provide Information", "Provider submits the requested additional information"),
    
    /**
     * Event to approve the treatment.
     * Transitions from UNDER_REVIEW to APPROVED.
     */
    APPROVE("Approve", "Approve the treatment authorization request"),
    
    /**
     * Event to deny the treatment.
     * Transitions from UNDER_REVIEW to DENIED.
     */
    DENY("Deny", "Deny the treatment authorization request");

    private final String displayName;
    private final String description;

    Events(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }
}