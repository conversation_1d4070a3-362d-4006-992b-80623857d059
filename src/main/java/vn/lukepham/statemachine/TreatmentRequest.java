package vn.lukepham.statemachine;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * DTO for creating a new treatment authorization request.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreatmentRequest {
    
    @NotBlank(message = "Provider ID is required")
    private String providerId;
    
    @NotBlank(message = "Provider name is required")
    private String providerName;
    
    @NotBlank(message = "Patient ID is required")
    private String patientId;
    
    @NotBlank(message = "Patient name is required")
    private String patientName;
    
    private String policyNumber;
    
    @NotBlank(message = "Treatment type is required")
    private String treatmentType;
    
    private String treatmentDescription;
    
    private String diagnosisCode;
    
    private String procedureCode;
    
    @NotNull(message = "Estimated cost is required")
    @Positive(message = "Estimated cost must be positive")
    private BigDecimal estimatedCost;
    
    private String priority = "MEDIUM"; // HIGH, MEDIUM, LOW
    
    private Boolean isEmergency = false;
}