package vn.lukepham.statemachine;

import vn.lukepham.statemachine.Events;
import vn.lukepham.statemachine.States;
import com.insurance.statemachine.dto.EventRequest;
import com.insurance.statemachine.dto.TreatmentRequest;
import com.insurance.statemachine.dto.TreatmentResponse;
import com.insurance.statemachine.entity.AuditLog;
import com.insurance.statemachine.entity.TreatmentAuthorization;
import com.insurance.statemachine.exception.InvalidStateTransitionException;
import com.insurance.statemachine.exception.TreatmentNotFoundException;
import com.insurance.statemachine.repository.AuditLogRepository;
import com.insurance.statemachine.repository.TreatmentAuthorizationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineEventResult;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachineRuntimePersister;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Service layer for managing treatment authorization state machines.
 * Handles creation, state transitions, and persistence of treatment requests.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TreatmentService {

    private final StateMachineFactory<States, Events> stateMachineFactory;
    private final StateMachineRuntimePersister<States, Events, String> stateMachineRuntimePersister;
    private final TreatmentAuthorizationRepository treatmentRepository;
    private final AuditLogRepository auditLogRepository;
    private final HttpServletRequest httpServletRequest;

    /**
     * Creates a new treatment authorization request and initializes its state machine.
     */
    @Transactional
    public TreatmentResponse createTreatment(TreatmentRequest request) {
        log.info("Creating new treatment authorization for patient: {}", request.getPatientName());
        
        // Generate unique IDs
        String treatmentId = UUID.randomUUID().toString();
        String stateMachineId = "SM-" + treatmentId;
        
        // Create and save the treatment entity
        TreatmentAuthorization treatment = TreatmentAuthorization.builder()
                .id(treatmentId)
                .stateMachineId(stateMachineId)
                .currentState(States.TREATMENT_SUBMITTED)
                .providerId(request.getProviderId())
                .providerName(request.getProviderName())
                .patientId(request.getPatientId())
                .patientName(request.getPatientName())
                .policyNumber(request.getPolicyNumber())
                .treatmentType(request.getTreatmentType())
                .treatmentDescription(request.getTreatmentDescription())
                .diagnosisCode(request.getDiagnosisCode())
                .procedureCode(request.getProcedureCode())
                .estimatedCost(request.getEstimatedCost())
                .priority(request.getPriority())
                .isEmergency(request.getIsEmergency())
                .createdBy(getCurrentUserId())
                .build();
        
        treatment = treatmentRepository.save(treatment);
        
        // Initialize state machine
        StateMachine<States, Events> stateMachine = stateMachineFactory.getStateMachine(stateMachineId);
        stateMachine.startReactively().subscribe();
        
        // Persist initial state
        try {
            stateMachineRuntimePersister.persist(stateMachine, stateMachineId);
        } catch (Exception e) {
            log.error("Error persisting state machine for treatment {}: {}", treatmentId, e.getMessage());
            throw new RuntimeException("Failed to initialize state machine", e);
        }
        
        // Create initial audit log
        createAuditLog(treatment, null, States.TREATMENT_SUBMITTED, Events.SUBMIT, "Treatment authorization created");
        
        log.info("Treatment authorization {} created successfully in state {}", treatmentId, States.TREATMENT_SUBMITTED);
        
        return toResponse(treatment);
    }

    /**
     * Retrieves a treatment authorization by ID.
     */
    @Transactional(readOnly = true)
    public TreatmentResponse getTreatment(String treatmentId) {
        TreatmentAuthorization treatment = treatmentRepository.findById(treatmentId)
                .orElseThrow(() -> new TreatmentNotFoundException("Treatment not found: " + treatmentId));
        
        return toResponse(treatment);
    }

    /**
     * Processes an event for a treatment authorization.
     * Validates the transition and updates the state machine.
     */
    @Transactional
    public TreatmentResponse processEvent(String treatmentId, EventRequest eventRequest) {
        log.info("Processing event {} for treatment {}", eventRequest.getEvent(), treatmentId);
        
        // Retrieve the treatment
        TreatmentAuthorization treatment = treatmentRepository.findById(treatmentId)
                .orElseThrow(() -> new TreatmentNotFoundException("Treatment not found: " + treatmentId));
        
        // Validate user permissions
        validateUserPermissions(eventRequest.getEvent());
        
        States previousState = treatment.getCurrentState();
        
        try {
            // Restore state machine from persistence
            StateMachine<States, Events> stateMachine = stateMachineFactory.getStateMachine(treatment.getStateMachineId());
            stateMachineRuntimePersister.restore(stateMachine, treatment.getStateMachineId());
            
            // Build message with headers for context
            Message<Events> message = MessageBuilder
                    .withPayload(eventRequest.getEvent())
                    .setHeader("treatmentId", treatmentId)
                    .setHeader("providerId", treatment.getProviderId())
                    .setHeader("userId", getCurrentUserId())
                    .setHeader("denialReason", eventRequest.getDenialReason())
                    .setHeader("infoRequired", eventRequest.getInfoRequired())
                    .setHeader("additionalInfo", eventRequest.getAdditionalInfo())
                    .build();
            
            // Send event and wait for result
            Flux<StateMachineEventResult<States, Events>> resultFlux = stateMachine.sendEvent(Mono.just(message));
            StateMachineEventResult<States, Events> result = resultFlux.blockFirst();
            
            if (result == null || result.getResultType() != StateMachineEventResult.ResultType.ACCEPTED) {
                throw new InvalidStateTransitionException(
                    String.format("Event '%s' is not valid in current state '%s'", 
                        eventRequest.getEvent(), previousState));
            }
            
            // Update treatment entity
            States newState = stateMachine.getState().getId();
            treatment.setCurrentState(newState);
            treatment.setLastModifiedBy(getCurrentUserId());
            
            // Update specific fields based on event
            updateTreatmentBasedOnEvent(treatment, eventRequest);
            
            treatment = treatmentRepository.save(treatment);
            
            // Persist state machine
            stateMachineRuntimePersister.persist(stateMachine, treatment.getStateMachineId());
            
            // Create audit log
            createAuditLog(treatment, previousState, newState, eventRequest.getEvent(), eventRequest.getNotes());
            
            log.info("Treatment {} transitioned from {} to {} via event {}", 
                treatmentId, previousState, newState, eventRequest.getEvent());
            
            return toResponse(treatment);
            
        } catch (InvalidStateTransitionException e) {
            // Log failed transition attempt
            createFailedAuditLog(treatment, previousState, eventRequest.getEvent(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error processing event {} for treatment {}: {}", 
                eventRequest.getEvent(), treatmentId, e.getMessage());
            createFailedAuditLog(treatment, previousState, eventRequest.getEvent(), e.getMessage());
            throw new RuntimeException("Failed to process event", e);
        }
    }

    /**
     * Updates treatment entity fields based on the event type.
     */
    private void updateTreatmentBasedOnEvent(TreatmentAuthorization treatment, EventRequest eventRequest) {
        LocalDateTime now = LocalDateTime.now();
        
        switch (eventRequest.getEvent()) {
            case ASSIGN_FOR_REVIEW:
                treatment.setAssignedReviewerId(eventRequest.getReviewerId());
                treatment.setAssignedReviewerName(eventRequest.getReviewerName());
                break;
                
            case REQUEST_INFO:
                treatment.setAdditionalInfoRequested(eventRequest.getInfoRequired());
                treatment.setInfoRequestedDate(now);
                break;
                
            case PROVIDE_INFO:
                treatment.setAdditionalInfoProvided(eventRequest.getAdditionalInfo());
                treatment.setInfoProvidedDate(now);
                break;
                
            case APPROVE:
                treatment.setDecisionDate(now);
                treatment.setApprovedAmount(eventRequest.getApprovedAmount());
                treatment.setApprovalConditions(eventRequest.getApprovalConditions());
                treatment.setAuthorizationExpiryDate(eventRequest.getExpiryDate());
                treatment.setReviewNotes(eventRequest.getNotes());
                break;
                
            case DENY:
                treatment.setDecisionDate(now);
                treatment.setDenialReason(eventRequest.getDenialReason());
                treatment.setReviewNotes(eventRequest.getNotes());
                break;
        }
    }

    /**
     * Validates user permissions for the requested event.
     */
    private void validateUserPermissions(Events event) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null || !auth.isAuthenticated()) {
            throw new SecurityException("User not authenticated");
        }
        
        boolean hasProviderRole = auth.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_PROVIDER"));
        boolean hasAdjusterRole = auth.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADJUSTER"));
        
        // Provider can only SUBMIT and PROVIDE_INFO
        if (hasProviderRole && !hasAdjusterRole) {
            if (event != Events.SUBMIT && event != Events.PROVIDE_INFO) {
                throw new SecurityException("Insufficient permissions for event: " + event);
            }
        }
        
        // Adjuster can perform all events except SUBMIT and PROVIDE_INFO
        if (hasAdjusterRole && !hasProviderRole) {
            if (event == Events.SUBMIT || event == Events.PROVIDE_INFO) {
                throw new SecurityException("Adjusters cannot perform provider events: " + event);
            }
        }
    }

    /**
     * Creates an audit log entry for a successful state transition.
     */
    private void createAuditLog(TreatmentAuthorization treatment, States fromState, 
                                States toState, Events event, String notes) {
        AuditLog auditLog = AuditLog.builder()
                .treatmentAuthorization(treatment)
                .fromState(fromState)
                .toState(toState)
                .event(event)
                .userId(getCurrentUserId())
                .userName(getCurrentUserName())
                .userRole(getCurrentUserRole())
                .notes(notes)
                .ipAddress(getClientIpAddress())
                .success(true)
                .build();
        
        auditLogRepository.save(auditLog);
    }

    /**
     * Creates an audit log entry for a failed state transition attempt.
     */
    private void createFailedAuditLog(TreatmentAuthorization treatment, States fromState, 
                                      Events event, String errorMessage) {
        AuditLog auditLog = AuditLog.builder()
                .treatmentAuthorization(treatment)
                .fromState(fromState)
                .toState(fromState) // State doesn't change on failure
                .event(event)
                .userId(getCurrentUserId())
                .userName(getCurrentUserName())
                .userRole(getCurrentUserRole())
                .ipAddress(getClientIpAddress())
                .success(false)
                .errorMessage(errorMessage)
                .build();
        
        auditLogRepository.save(auditLog);
    }

    /**
     * Converts a TreatmentAuthorization entity to a response DTO.
     */
    private TreatmentResponse toResponse(TreatmentAuthorization treatment) {
        return TreatmentResponse.builder()
                .id(treatment.getId())
                .currentState(treatment.getCurrentState())
                .stateDisplayName(treatment.getCurrentState().getDisplayName())
                .stateDescription(treatment.getCurrentState().getDescription())
                .providerId(treatment.getProviderId())
                .providerName(treatment.getProviderName())
                .patientId(treatment.getPatientId())
                .patientName(treatment.getPatientName())
                .policyNumber(treatment.getPolicyNumber())
                .treatmentType(treatment.getTreatmentType())
                .treatmentDescription(treatment.getTreatmentDescription())
                .diagnosisCode(treatment.getDiagnosisCode())
                .procedureCode(treatment.getProcedureCode())
                .estimatedCost(treatment.getEstimatedCost())
                .approvedAmount(treatment.getApprovedAmount())
                .assignedReviewerId(treatment.getAssignedReviewerId())
                .assignedReviewerName(treatment.getAssignedReviewerName())
                .reviewNotes(treatment.getReviewNotes())
                .additionalInfoRequested(treatment.getAdditionalInfoRequested())
                .additionalInfoProvided(treatment.getAdditionalInfoProvided())
                .denialReason(treatment.getDenialReason())
                .approvalConditions(treatment.getApprovalConditions())
                .priority(treatment.getPriority())
                .isEmergency(treatment.getIsEmergency())
                .authorizationExpiryDate(treatment.getAuthorizationExpiryDate())
                .createdAt(treatment.getCreatedAt())
                .updatedAt(treatment.getUpdatedAt())
                .availableEvents(getAvailableEvents(treatment.getCurrentState()))
                .build();
    }

    /**
     * Gets available events for the current state.
     */
    private Events[] getAvailableEvents(States currentState) {
        switch (currentState) {
            case TREATMENT_SUBMITTED:
                return new Events[]{Events.ASSIGN_FOR_REVIEW};
            case UNDER_REVIEW:
                return new Events[]{Events.REQUEST_INFO, Events.APPROVE, Events.DENY};
            case ADDITIONAL_INFO_REQUIRED:
                return new Events[]{Events.PROVIDE_INFO};
            case APPROVED:
            case DENIED:
                return new Events[]{}; // Final states have no transitions
            default:
                return new Events[]{};
        }
    }

    private String getCurrentUserId() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return auth != null ? auth.getName() : "SYSTEM";
    }

    private String getCurrentUserName() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return auth != null ? auth.getName() : "SYSTEM";
    }

    private String getCurrentUserRole() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && !auth.getAuthorities().isEmpty()) {
            return auth.getAuthorities().iterator().next().getAuthority();
        }
        return "UNKNOWN";
    }

    private String getClientIpAddress() {
        if (httpServletRequest == null) {
            return "UNKNOWN";
        }
        
        String xForwardedFor = httpServletRequest.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = httpServletRequest.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return httpServletRequest.getRemoteAddr();
    }
}