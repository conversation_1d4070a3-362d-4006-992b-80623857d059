package vn.lukepham.statemachine;

import Events;
import States;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * JPA Entity for auditing state transitions.
 * Records every state change for compliance and debugging purposes.
 */
@Entity
@Table(name = "audit_log")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Reference to the treatment authorization.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "treatment_authorization_id", nullable = false)
    private TreatmentAuthorization treatmentAuthorization;
    
    /**
     * State before the transition.
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "from_state")
    private States fromState;
    
    /**
     * State after the transition.
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "to_state", nullable = false)
    private States toState;
    
    /**
     * Event that triggered the transition.
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "event", nullable = false)
    private Events event;
    
    /**
     * User who initiated the event.
     */
    @Column(name = "user_id", nullable = false)
    private String userId;
    
    @Column(name = "user_name")
    private String userName;
    
    @Column(name = "user_role")
    private String userRole;
    
    /**
     * Timestamp of the transition.
     */
    @CreationTimestamp
    @Column(name = "timestamp", nullable = false, updatable = false)
    private LocalDateTime timestamp;
    
    /**
     * Additional context or notes about the transition.
     */
    @Column(name = "notes", length = 1000)
    private String notes;
    
    /**
     * IP address of the user making the change.
     */
    @Column(name = "ip_address")
    private String ipAddress;
    
    /**
     * Indicates if the transition was successful.
     */
    @Column(name = "success")
    private Boolean success = true;
    
    /**
     * Error message if the transition failed.
     */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;
}