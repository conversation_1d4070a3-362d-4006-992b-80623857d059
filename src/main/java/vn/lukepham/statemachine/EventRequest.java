package vn.lukepham.statemachine;

import Events;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO for processing an event on a treatment authorization.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventRequest {
    
    @NotNull(message = "Event is required")
    private Events event;
    
    // For ASSIGN_FOR_REVIEW event
    private String reviewerId;
    private String reviewerName;
    
    // For REQUEST_INFO event
    private String infoRequired;
    
    // For PROVIDE_INFO event
    private String additionalInfo;
    
    // For APPROVE event
    private BigDecimal approvedAmount;
    private String approvalConditions;
    private LocalDateTime expiryDate;
    
    // For DENY event
    private String denialReason;
    
    // General notes for audit
    private String notes;
}