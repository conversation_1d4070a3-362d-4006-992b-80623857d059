package vn.lukepham.statemachine;

import vn.lukepham.statemachine.Events;
import vn.lukepham.statemachine.States;
import com.insurance.statemachine.service.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.persist.StateMachineRuntimePersister;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.transition.Transition;

import java.util.EnumSet;

/**
 * Configuration class for the Treatment Authorization State Machine.
 * Defines states, transitions, actions, and listeners for managing
 * the lifecycle of insurance treatment authorization requests.
 */
@Slf4j
@Configuration
@EnableStateMachineFactory
@RequiredArgsConstructor
public class StateMachineConfig extends EnumStateMachineConfigurerAdapter<States, Events> {

    private final NotificationService notificationService;
    private final StateMachineRuntimePersister<States, Events, String> stateMachineRuntimePersister;

    /**
     * Configure the states of the state machine.
     * Defines all possible states and marks APPROVED and DENIED as end states.
     */
    @Override
    public void configure(StateMachineStateConfigurer<States, Events> states) throws Exception {
        states
            .withStates()
                .initial(States.TREATMENT_SUBMITTED)
                .states(EnumSet.allOf(States.class))
                .end(States.APPROVED)
                .end(States.DENIED)
                .stateEntry(States.ADDITIONAL_INFO_REQUIRED, sendInfoRequestAction())
                .stateEntry(States.APPROVED, sendApprovalNotificationAction())
                .stateEntry(States.DENIED, sendDenialNotificationAction());
    }

    /**
     * Configure the transitions between states.
     * Defines which events trigger state changes and the associated business logic.
     */
    @Override
    public void configure(StateMachineTransitionConfigurer<States, Events> transitions) throws Exception {
        transitions
            // Initial submission
            .withExternal()
                .source(States.TREATMENT_SUBMITTED)
                .target(States.UNDER_REVIEW)
                .event(Events.ASSIGN_FOR_REVIEW)
                .action(logTransitionAction())
            
            // Request additional information
            .and()
            .withExternal()
                .source(States.UNDER_REVIEW)
                .target(States.ADDITIONAL_INFO_REQUIRED)
                .event(Events.REQUEST_INFO)
                .action(logTransitionAction())
            
            // Provider submits additional information
            .and()
            .withExternal()
                .source(States.ADDITIONAL_INFO_REQUIRED)
                .target(States.UNDER_REVIEW)
                .event(Events.PROVIDE_INFO)
                .action(logTransitionAction())
            
            // Approval path
            .and()
            .withExternal()
                .source(States.UNDER_REVIEW)
                .target(States.APPROVED)
                .event(Events.APPROVE)
                .action(logTransitionAction())
            
            // Denial path
            .and()
            .withExternal()
                .source(States.UNDER_REVIEW)
                .target(States.DENIED)
                .event(Events.DENY)
                .action(logTransitionAction());
    }

    /**
     * Configure the state machine settings.
     * Sets up listeners and persistence configuration.
     */
    @Override
    public void configure(StateMachineConfigurationConfigurer<States, Events> config) throws Exception {
        config
            .withConfiguration()
                .autoStartup(true)
                .listener(stateMachineListener())
                .and()
            .withPersistence()
                .runtimePersister(stateMachineRuntimePersister);
    }

    /**
     * State machine listener for auditing and debugging.
     * Logs all state changes and transitions.
     */
    @Bean
    public StateMachineListener<States, Events> stateMachineListener() {
        return new StateMachineListenerAdapter<States, Events>() {
            @Override
            public void stateChanged(State<States, Events> from, State<States, Events> to) {
                log.info("State changed from {} to {}", 
                    from != null ? from.getId() : "INITIAL", 
                    to.getId());
            }

            @Override
            public void transition(Transition<States, Events> transition) {
                if (transition.getSource() != null && transition.getTarget() != null) {
                    log.info("Transition: {} -> {} on event {}", 
                        transition.getSource().getId(),
                        transition.getTarget().getId(),
                        transition.getTrigger().getEvent());
                }
            }

            @Override
            public void stateMachineError(StateMachine<States, Events> stateMachine, Exception exception) {
                log.error("State machine error in state {}: {}", 
                    stateMachine.getState().getId(), 
                    exception.getMessage(), 
                    exception);
            }
        };
    }

    /**
     * Action to log transitions for audit purposes.
     */
    private Action<States, Events> logTransitionAction() {
        return context -> {
            String treatmentId = context.getMessageHeaders().get("treatmentId", String.class);
            String userId = context.getMessageHeaders().get("userId", String.class);
            
            log.info("Transition action executed - Treatment: {}, User: {}, Event: {}", 
                treatmentId, userId, context.getEvent());
            
            // Additional audit logging can be implemented here
        };
    }

    /**
     * Action triggered when entering ADDITIONAL_INFO_REQUIRED state.
     * Sends notification to provider requesting additional information.
     */
    private Action<States, Events> sendInfoRequestAction() {
        return context -> {
            String treatmentId = context.getMessageHeaders().get("treatmentId", String.class);
            String providerId = context.getMessageHeaders().get("providerId", String.class);
            String infoRequired = context.getMessageHeaders().get("infoRequired", String.class);
            
            log.info("Sending information request for treatment {} to provider {}", 
                treatmentId, providerId);
            
            notificationService.sendInfoRequest(treatmentId, providerId, infoRequired);
        };
    }

    /**
     * Action triggered when entering APPROVED state.
     * Sends approval notification to provider.
     */
    private Action<States, Events> sendApprovalNotificationAction() {
        return context -> {
            String treatmentId = context.getMessageHeaders().get("treatmentId", String.class);
            String providerId = context.getMessageHeaders().get("providerId", String.class);
            
            log.info("Sending approval notification for treatment {} to provider {}", 
                treatmentId, providerId);
            
            notificationService.sendApprovalNotification(treatmentId, providerId);
        };
    }

    /**
     * Action triggered when entering DENIED state.
     * Sends denial notification with reason to provider.
     */
    private Action<States, Events> sendDenialNotificationAction() {
        return context -> {
            String treatmentId = context.getMessageHeaders().get("treatmentId", String.class);
            String providerId = context.getMessageHeaders().get("providerId", String.class);
            String denialReason = context.getMessageHeaders().get("denialReason", String.class);
            
            log.info("Sending denial notification for treatment {} to provider {} with reason: {}", 
                treatmentId, providerId, denialReason);
            
            notificationService.sendDenialNotification(treatmentId, providerId, denialReason);
        };
    }
}