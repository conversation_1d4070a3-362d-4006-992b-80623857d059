package vn.lukepham.statemachine;

/**
 * Enum representing all possible states in the treatment authorization lifecycle.
 */
public enum States {
    /**
     * Initial state when a healthcare provider submits a new treatment plan.
     * The request is pending initial review.
     */
    TREATMENT_SUBMITTED("Treatment Submitted", "The treatment request has been submitted and is awaiting review"),
    
    /**
     * The request has been assigned to a claims adjuster or medical reviewer.
     * It is actively being examined for medical necessity, policy coverage, and cost-effectiveness.
     */
    UNDER_REVIEW("Under Review", "The treatment request is being actively reviewed by an adjuster"),
    
    /**
     * The reviewer has determined that more information is needed from the provider.
     * This state pauses the process until the provider responds.
     */
    ADDITIONAL_INFO_REQUIRED("Additional Information Required", "Additional information has been requested from the provider"),
    
    /**
     * The treatment request has been fully approved. This is a final state.
     */
    APPROVED("Approved", "The treatment request has been approved"),
    
    /**
     * The treatment request has been denied. The reason for denial must be captured.
     * This is a final state.
     */
    DENIED("Denied", "The treatment request has been denied");

    private final String displayName;
    private final String description;

    States(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }
}