package vn.lukepham.statemachine;

import Events;
import States;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO for treatment authorization response.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreatmentResponse {
    
    private String id;
    private States currentState;
    private String stateDisplayName;
    private String stateDescription;
    
    // Provider and Patient info
    private String providerId;
    private String providerName;
    private String patientId;
    private String patientName;
    private String policyNumber;
    
    // Treatment details
    private String treatmentType;
    private String treatmentDescription;
    private String diagnosisCode;
    private String procedureCode;
    private BigDecimal estimatedCost;
    private BigDecimal approvedAmount;
    
    // Review details
    private String assignedReviewerId;
    private String assignedReviewerName;
    private String reviewNotes;
    
    // Additional info tracking
    private String additionalInfoRequested;
    private String additionalInfoProvided;
    
    // Decision details
    private String denialReason;
    private String approvalConditions;
    private LocalDateTime authorizationExpiryDate;
    
    // Metadata
    private String priority;
    private Boolean isEmergency;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Available actions
    private Events[] availableEvents;
}