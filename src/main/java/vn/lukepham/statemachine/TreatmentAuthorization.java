package vn.lukepham.statemachine;

import States;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * JPA Entity representing a treatment authorization request.
 * Stores all relevant information about the treatment and its current state.
 */
@Entity
@Table(name = "treatment_authorization")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreatmentAuthorization {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    /**
     * Unique identifier for the state machine instance associated with this treatment.
     */
    @Column(name = "state_machine_id", unique = true)
    private String stateMachineId;
    
    /**
     * Current state of the treatment authorization request.
     */
    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "current_state", nullable = false)
    private States currentState;
    
    /**
     * Healthcare provider information.
     */
    @NotBlank
    @Column(name = "provider_id", nullable = false)
    private String providerId;
    
    @NotBlank
    @Column(name = "provider_name", nullable = false)
    private String providerName;
    
    /**
     * Patient information.
     */
    @NotBlank
    @Column(name = "patient_id", nullable = false)
    private String patientId;
    
    @NotBlank
    @Column(name = "patient_name", nullable = false)
    private String patientName;
    
    @Column(name = "policy_number")
    private String policyNumber;
    
    /**
     * Treatment details.
     */
    @NotBlank
    @Column(name = "treatment_type", nullable = false)
    private String treatmentType;
    
    @Column(name = "treatment_description", length = 2000)
    private String treatmentDescription;
    
    @Column(name = "diagnosis_code")
    private String diagnosisCode;
    
    @Column(name = "procedure_code")
    private String procedureCode;
    
    /**
     * Financial information.
     */
    @Column(name = "estimated_cost", precision = 10, scale = 2)
    private BigDecimal estimatedCost;
    
    @Column(name = "approved_amount", precision = 10, scale = 2)
    private BigDecimal approvedAmount;
    
    /**
     * Review information.
     */
    @Column(name = "assigned_reviewer_id")
    private String assignedReviewerId;
    
    @Column(name = "assigned_reviewer_name")
    private String assignedReviewerName;
    
    @Column(name = "review_notes", length = 2000)
    private String reviewNotes;
    
    /**
     * Additional information tracking.
     */
    @Column(name = "additional_info_requested", length = 2000)
    private String additionalInfoRequested;
    
    @Column(name = "additional_info_provided", length = 2000)
    private String additionalInfoProvided;
    
    @Column(name = "info_requested_date")
    private LocalDateTime infoRequestedDate;
    
    @Column(name = "info_provided_date")
    private LocalDateTime infoProvidedDate;
    
    /**
     * Decision details.
     */
    @Column(name = "decision_date")
    private LocalDateTime decisionDate;
    
    @Column(name = "denial_reason", length = 1000)
    private String denialReason;
    
    @Column(name = "approval_conditions", length = 1000)
    private String approvalConditions;
    
    /**
     * Audit fields.
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private String createdBy;
    
    @Column(name = "last_modified_by")
    private String lastModifiedBy;
    
    /**
     * Relationship to audit logs for tracking all state transitions.
     */
    @OneToMany(mappedBy = "treatmentAuthorization", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("timestamp DESC")
    private List<AuditLog> auditLogs = new ArrayList<>();
    
    /**
     * Priority level for the treatment request.
     */
    @Column(name = "priority")
    private String priority; // HIGH, MEDIUM, LOW
    
    /**
     * Indicates if this is an emergency treatment.
     */
    @Column(name = "is_emergency")
    private Boolean isEmergency = false;
    
    /**
     * Expiration date for the authorization if approved.
     */
    @Column(name = "authorization_expiry_date")
    private LocalDateTime authorizationExpiryDate;
}