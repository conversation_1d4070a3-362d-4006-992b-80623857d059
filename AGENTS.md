# Repository Guidelines

## Project Structure & Module Organization
- `src/main/java/vn/lukepham/...` holds Spring components; group packages by layer (`config`, `domain`, `service`, `web`) to keep the state-machine flow easy to trace.
- `src/main/resources/templates` stores server-side views; `src/main/resources/static` contains bundled JS/CSS assets shared across flows.
- `src/test/java/vn/lukepham/...` mirrors the main package layout for JUnit 5 and MockMvc tests.
- `pom.xml` is the single source of truth for dependencies, Lombok processors, and build plugins—edit it instead of per-module overrides.

## Build, Test, and Development Commands
- `./mvnw spring-boot:run` (or `mvnw.cmd` on Windows) starts the app with DevTools reload and the in-memory H2 profile.
- `./mvnw clean package` produces a runnable JAR in `target/` for deployment checks.
- `./mvnw test` executes unit and integration tests; run it before every push.
- `./mvnw dependency:analyze` helps identify unused or missing dependencies when modules change.

## Coding Style & Naming Conventions
- Target Java 17, 4-space indentation, braces on the same line, and Lombok annotations (`@Getter`, `@Builder`) where it reduces boilerplate.
- Classes use PascalCase, methods and fields use camelCase, constants use SCREAMING_SNAKE_CASE.
- Prefix controllers with `*Controller`, services with `*Service`, and Spring configs with `*Configuration` for quick discovery.
- Keep DTOs immutable when possible and co-locate state-transition enums with the aggregate they govern.

## Testing Guidelines
- Default to `@SpringBootTest` for cross-layer state-machine scenarios and slice tests (`@WebMvcTest`, `@DataJpaTest`) for focused checks.
- Name test classes `SomethingTests` and individual methods `shouldDoSomething_whenCondition` to express intent.
- Use the bundled H2 database for persistence tests; seed data in `src/test/resources/data.sql` when needed.
- Aim for coverage on all transition paths; document edge cases in test Javadoc or method comments.

## Commit & Pull Request Guidelines
- Follow Conventional Commits (`feat:`, `fix:`, `refactor:`, etc.) so changelog generation stays predictable.
- One feature or bugfix per PR, with a short summary, testing notes (`./mvnw test` output), and linked issue if available.
- Request review only after resolving warnings and ensuring new modules are wired into the Spring context.

## Configuration Tips
- Store environment-specific overrides in a profile file such as `application-dev.properties`; keep secrets in external config or environment variables.
- Document any new state transition schema updates in `HELP.md` so operators can reference them without scanning the code.
